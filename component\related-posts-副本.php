<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<div class="related-posts bg-white dark:bg-[#161829] rounded-lg p-4 md:p-6 border border-stone-100 dark:border-neutral-600">
    <div class="mb-4 md:mb-6">
        <h5 class="text-lg font-medium text-black dark:text-gray-300 flex items-center justify-center">
            <iconify-icon icon="tabler:heart" class="mr-2 text-xl text-red-500"></iconify-icon>猜你喜欢
        </h5>
    </div>
    
    <?php
    // 获取热门文章并随机推荐
    $db = Typecho_Db::get();
    
    // 计算一个月前的时间戳（扩大范围到一个月，增加文章池）
    $monthAgo = time() - 30 * 24 * 60 * 60; // 一个月前的时间戳
    
    // 查询近期热门文章，获取较大范围的热门文章池
    try {
        $hotPosts = $db->fetchAll($db
            ->select()->from('table.contents')
            ->where('status = ?', 'publish')
            ->where('type = ?', 'post')
            ->where('created > ?', $monthAgo) // 一个月内的文章
            ->order('views', Typecho_Db::SORT_DESC)
            ->limit(30) // 获取前30篇热门文章作为推荐池
        );
        
        // 判断是否获取到文章
        if (!empty($hotPosts)):
            $posts = [];
            foreach ($hotPosts as $post) {
                // 创建文章查询对象以正确生成永久链接
                $postObj = Typecho_Widget::widget('Widget_Abstract_Contents')->push($post);
                $posts[] = [
                    'permalink' => $postObj['permalink'],
                    'title' => $post['title'],
                ];
            }
            
            // 随机打乱文章顺序，实现每次刷新看到不同的推荐
            shuffle($posts);
            
            // 确保最多只有6篇文章
            $posts = array_slice($posts, 0, 6);
            
            // 将文章分成左右两列
            $leftPosts = [];
            $rightPosts = [];
            foreach ($posts as $index => $post) {
                if ($index % 2 == 0) {
                    $leftPosts[] = ['index' => $index, 'post' => $post];
                } else {
                    $rightPosts[] = ['index' => $index, 'post' => $post];
                }
            }
    ?>
        <div class="flex">
            <div class="w-1/2 pr-2 md:pr-3">
                <?php foreach ($leftPosts as $item): ?>
                    <a href="<?php echo $item['post']['permalink']; ?>" class="related-post-item block group rounded-md mb-2 md:mb-3">
                        <div class="flex items-center">
                            <div class="num-icon-wrapper">
                                <span class="num-icon"><?php echo $item['index'] + 1; ?></span>
                            </div>
                            <span class="truncate text-gray-700 dark:text-gray-300"><?php echo $item['post']['title']; ?></span>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
            <div class="w-1/2 pl-2 md:pl-3">
                <?php foreach ($rightPosts as $item): ?>
                    <a href="<?php echo $item['post']['permalink']; ?>" class="related-post-item block group rounded-md mb-2 md:mb-3">
                        <div class="flex items-center">
                            <div class="num-icon-wrapper">
                                <span class="num-icon"><?php echo $item['index'] + 1; ?></span>
                            </div>
                            <span class="truncate text-gray-700 dark:text-gray-300"><?php echo $item['post']['title']; ?></span>
                        </div>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php 
        else: 
    ?>
        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
            <iconify-icon icon="tabler:alert-circle" class="block mx-auto text-2xl mb-2"></iconify-icon>
            暂无推荐文章
        </div>
    <?php 
        endif;
    } catch (Exception $e) {
        // 如果出现异常，显示错误信息（仅在调试模式下）
        if (defined('__TYPECHO_DEBUG__') && __TYPECHO_DEBUG__) {
            echo '<div class="text-center py-4 text-red-500">';
            echo '获取文章时出错: ' . $e->getMessage();
            echo '</div>';
        } else {
            echo '<div class="text-center py-4 text-gray-500 dark:text-gray-400">';
            echo '<iconify-icon icon="tabler:alert-circle" class="block mx-auto text-2xl mb-2"></iconify-icon>';
            echo '暂无推荐文章';
            echo '</div>';
        }
    }
    ?>
</div>

<style>
.related-posts .related-post-item {
    position: relative;
    color: #333;
    transition: all 0.2s;
    overflow: hidden;
    background-color: transparent;
    padding: 12px !important;
    border: none;
    background: transparent;
}

.dark .related-posts .related-post-item {
    color: #e0e0e0;
    background: transparent;
}

.related-posts .related-post-item::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, #3b82f6 0%, transparent 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
    border-radius: 0.375rem;
}

.related-posts .related-post-item:hover {
    transform: translateY(-2px);
}

.related-posts .related-post-item:hover::before {
    opacity: 0.08;
}

.related-posts .num-icon-wrapper {
    width: 24px;
    min-width: 24px;
    margin-right: 10px;
}

.related-posts .num-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    min-width: 22px;
    border-radius: 50%;
    background: #eee;
    color: #666;
    font-size: 13px;
    position: relative;
    z-index: 1;
    transition: all 0.2s;
}

.related-posts .related-post-item:hover .num-icon {
    background: #3b82f6;
    color: white;
    transform: scale(1.1);
}

.dark .related-posts .num-icon {
    background: #374151;
    color: #aaa;
}

.dark .related-posts .related-post-item:hover .num-icon {
    background: #60a5fa;
    color: #1e293b;
}

.related-posts .related-post-item span.truncate {
    position: relative;
    z-index: 1;
    transition: color 0.3s ease;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    font-size: 15px !important;
}

.related-posts .related-post-item:hover span.truncate {
    color: #3b82f6;
}

.dark .related-posts .related-post-item:hover span.truncate {
    color: #60a5fa;
}

@media (max-width: 640px) {
    .related-posts .related-post-item span.truncate {
        font-size: 13px !important;
    }
    .related-posts .num-icon {
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
    .related-posts .related-post-item {
        padding: 10px !important;
    }
    .related-posts .num-icon-wrapper {
        width: 22px;
        min-width: 22px;
        margin-right: 8px;
    }
}

@media (max-width: 480px) {
    .related-posts .num-icon {
        width: 18px;
        height: 18px;
        font-size: 11px;
    }
    .related-posts .related-post-item {
        padding: 8px !important;
    }
    .related-posts .related-post-item span.truncate {
        font-size: 12px !important;
    }
    .related-posts .num-icon-wrapper {
        width: 20px;
        min-width: 20px;
        margin-right: 6px;
    }
}
</style> 