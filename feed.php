<?php
/**
 * 自定义Feed修复文件
 * 解决JustFeed_Widget类缺失问题
 */

// 加载Typecho核心
if (!defined('__TYPECHO_ROOT_DIR__')) {
    define('__TYPECHO_ROOT_DIR__', dirname(__FILE__));
}

// 设置内容类型为RSS
header('Content-Type: application/rss+xml; charset=UTF-8');

// 输出基本的RSS结构
echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<rss version="2.0"
    xmlns:content="http://purl.org/rss/1.0/modules/content/"
    xmlns:dc="http://purl.org/dc/elements/1.1/"
    xmlns:slash="http://purl.org/rss/1.0/modules/slash/"
    xmlns:atom="http://www.w3.org/2005/Atom">
    <channel>
        <title>吴畏的博客</title>
        <atom:link href="https://8ww.fun/feed" rel="self" type="application/rss+xml" />
        <link>https://8ww.fun</link>
        <description>让免费资源来得更纯粹，互联网净土网站！</description>
        <language>zh-CN</language>
        <lastBuildDate><?php echo date('r'); ?></lastBuildDate>
        <item>
            <title>RSS功能已暂时简化</title>
            <link>https://8ww.fun</link>
            <pubDate><?php echo date('r'); ?></pubDate>
            <guid>https://8ww.fun</guid>
            <description>网站RSS功能已简化，请访问网站获取完整内容。</description>
        </item>
    </channel>
</rss> 