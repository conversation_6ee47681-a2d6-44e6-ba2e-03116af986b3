<?php
if (!defined('__TYPECHO_ROOT_DIR__')) exit;

/**
 * 网盘下载按钮处理类
 */
class DownloadButtonHandler {
    /**
     * 资源是否已加载
     */
    private static $loaded = false;
    
    /**
     * 初始化下载按钮功能
     */
    public static function init() {
        // 防止重复初始化
        if (self::$loaded) return;
        
        // 注册资源加载钩子
        Typecho_Plugin::factory('Widget_Archive')->header = array('DownloadButtonHandler', 'loadCss');
        Typecho_Plugin::factory('Widget_Archive')->footer = array('DownloadButtonHandler', 'loadJs');
        
        // 标记为已加载
        self::$loaded = true;
    }
    
    /**
     * 加载CSS文件
     */
    public static function loadCss() {
        $cssUrl = Helper::options()->themeUrl . '/assets/css/download-btn.css';
        echo '<link rel="stylesheet" href="' . $cssUrl . '?v=' . filemtime(__TYPECHO_ROOT_DIR__ . __TYPECHO_THEME_DIR__ . '/assets/css/download-btn.css') . '">';
    }
    
    /**
     * 加载JavaScript文件
     */
    public static function loadJs() {
        $jsUrl = Helper::options()->themeUrl . '/assets/js/download-btn.js';
        echo '<script src="' . $jsUrl . '?v=' . filemtime(__TYPECHO_ROOT_DIR__ . __TYPECHO_THEME_DIR__ . '/assets/js/download-btn.js') . '"></script>';
    }
}

/**
 * 网盘转换器类 - 新版本
 * 使用DOMDocument处理HTML而不是正则表达式，更可靠的识别和替换
 */
class DownloadButtonConverter {
    // 网盘域名和类型映射
    private $netdiskMap = [
        // 百度网盘
        'pan.baidu.com' => ['百度网盘', '#3b82f6'],
        'baidu.com/s/' => ['百度网盘', '#3b82f6'],
        'baidu.com/share' => ['百度网盘', '#3b82f6'],
        
        // 阿里云盘
        'aliyundrive.com' => ['阿里云盘', '#f59e0b'],
        'alipan.com' => ['阿里云盘', '#f59e0b'],
        
        // 天翼云盘
        'cloud.189.cn' => ['天翼云盘', '#0ea5e9'],
        '189.cn/t/' => ['天翼云盘', '#0ea5e9'],
        
        // 蓝奏云盘和部分常见变种 (其余由正则表达式匹配)
        'lanzou' => ['蓝奏云盘', '#06b6d4'],
        'lanz' => ['蓝奏云盘', '#06b6d4'],
        'lanzous' => ['蓝奏云盘', '#06b6d4'],
        'lanzoux' => ['蓝奏云盘', '#06b6d4'],
        'lanzoui' => ['蓝奏云盘', '#06b6d4'],
        'lanzoul' => ['蓝奏云盘', '#06b6d4'],
        'lanzoum' => ['蓝奏云盘', '#06b6d4'],
        'lanzouw' => ['蓝奏云盘', '#06b6d4'],
        'lanzouo' => ['蓝奏云盘', '#06b6d4'],
        'lanzoup' => ['蓝奏云盘', '#06b6d4'],
        'lanzouj' => ['蓝奏云盘', '#06b6d4'],
        'lanzout' => ['蓝奏云盘', '#06b6d4'],
        'xingyun' => ['蓝奏云盘', '#06b6d4'],
        
        // 飞机盘变种
        'feijix' => ['飞机盘', '#6366f1'],
        'feiji' => ['飞机盘', '#6366f1'],
        
        // 123网盘变种
        '123pan.com' => ['123网盘', '#8b5cf6'],
        '123pan.' => ['123网盘', '#8b5cf6'],
        '123684.' => ['123网盘', '#8b5cf6'],
        '123.' => ['123网盘', '#8b5cf6'],
        
        // 其他网盘
        'quark.cn' => ['夸克网盘', '#10b981'],
        'weiyun.com' => ['微云网盘', '#14b8a6'],
        'share.weiyun.com' => ['微云网盘', '#14b8a6'],
        '115.com' => ['115网盘', '#f43f5e'],
        '115.com/s/' => ['115网盘', '#f43f5e'],
        'ctfile.com' => ['城通网盘', '#ec4899'],
        'cowtransfer.com' => ['奶牛快传', '#0284c7'],
        'qingfuwu.cn' => ['奶牛快传', '#0284c7'],
        'cowtransfer.cc' => ['奶牛快传', '#0284c7'],
        'jianguoyun.com' => ['坚果云盘', '#2563eb'],
        '360.cn/yunpan' => ['360云盘', '#22c55e'],
        'yunpan.360.cn' => ['360云盘', '#22c55e'],
        'xunlei.com' => ['迅雷云盘', '#0ea5e9'],
        'pan.xunlei' => ['迅雷云盘', '#0ea5e9'],
        'xiaomi.com/disk' => ['小米云盘', '#f97316'],
        'pan.xiaomi.com' => ['小米云盘', '#f97316'],
        'mi.com/drive' => ['小米云盘', '#f97316'],
        'cloud.139.com' => ['移动云盘', '#22c55e'],
        '139.com/share' => ['移动云盘', '#22c55e'],
        'wo.cn/yun' => ['联通云盘', '#16a34a'],
        'wo.cn/cloud' => ['联通云盘', '#16a34a'],
        'pan.wo.cn' => ['联通云盘', '#16a34a'],
        'wocloud.com.cn' => ['联通云盘', '#16a34a'],
        'uc.cn' => ['UC网盘', '#ffa100'],
        'pan.uc.cn' => ['UC网盘', '#ffa100'],
        'weibo.com/p/' => ['新浪微盘', '#f43f5e'],
        'pan.sina.com.cn' => ['新浪微盘', '#f43f5e'],
        'tmp.link' => ['TMP.link', '#8b5cf6'],
        'pan.bitqiu.com' => ['比特球云盘', '#f59e0b'],
        'bitqiu.com' => ['比特球云盘', '#f59e0b'],
        '123wzwp.com' => ['123网盘外链', '#dc2626'],
        'pan.bilnn.com' => ['比邻云盘', '#0ea5e9'],
        'bilnn.com' => ['比邻云盘', '#0ea5e9'],
        'icloud.com' => ['iCloud云盘', '#3b82f6'],
        'icloud.com.cn' => ['iCloud云盘', '#3b82f6'],
        'apple.com/icloud' => ['iCloud云盘', '#3b82f6'],
        'firefox.com/send' => ['火狐网盘', '#ff7139'],
        'send.firefox.com' => ['火狐网盘', '#ff7139'],
        'wenshushu.cn' => ['文叔叔', '#4c1d95'],
        'wps.cn' => ['WPS云盘', '#0284c7'],
        'pan.wps.cn' => ['WPS云盘', '#0284c7'],
        'cloud.huawei.com' => ['华为云空间', '#d22222'],
        'pan.huawei.com' => ['华为云空间', '#d22222'],
        'caiyun.139.com' => ['天翼云盘', '#0ea5e9'],
        'caiyun.feixin.10086.cn' => ['移动云盘', '#22c55e'],
        'quqi.com' => ['曲奇云盘', '#e11d48'],
        'alist.nn.ci' => ['Alist网盘', '#12b7f5'],
        'kodcloud.com' => ['可道云', '#0ea5e9'],
        'webdav.com' => ['WebDAV网盘', '#3b82f6'],
        'seafile.com' => ['Seafile网盘', '#0c64c0'],
        
        // 手机厂商云服务
        'cloud.vivo.com.cn' => ['vivo云服务', '#0090e0'],
        'vivo.com/cloud' => ['vivo云服务', '#0090e0'],
        'i.vivo.com.cn' => ['vivo云服务', '#0090e0'],
        'cloud.oppo.com' => ['OPPO云服务', '#10b981'],
        'pan.oppo.com' => ['OPPO云服务', '#10b981'],
        'heytap.com' => ['OPPO云服务', '#10b981'],
        'oneplus.com/cloud' => ['一加云服务', '#eb0028'],
        'account.oneplus.com' => ['一加云服务', '#eb0028'],
        'cloud.flyme.cn' => ['魅族云服务', '#008cff'],
        'flyme.cn/cloud' => ['魅族云服务', '#008cff'],
        'account.meizu.com' => ['魅族云服务', '#008cff'],
        'cloud.smartisan.com' => ['坚果云服务', '#ff4a00'],
        'cloud.lenovo.com' => ['联想云服务', '#e60012'],
        'lenovo.com/cloud' => ['联想云服务', '#e60012'],
        'cloud.letv.com' => ['乐视云存储', '#e60012'],
        'nubia.com/cloud' => ['努比亚云服务', '#cf0a2c'],
        'cloud.zte.com.cn' => ['中兴云服务', '#0163b0'],
        'zte.com.cn/cloud' => ['中兴云服务', '#0163b0'],
        'yun.iqoo.com' => ['iQOO云服务', '#0052d9'],
        'realme.com/cloud' => ['realme云服务', '#ffcd00'],
        'cloud.honor.cn' => ['荣耀云服务', '#000000'],
        'account.honor.cn' => ['荣耀云服务', '#000000'],
        'cloud.samsung.com' => ['三星云服务', '#1428a0'],
        'samsungcloud.com' => ['三星云服务', '#1428a0'],
        
        // 国外网盘
        'mega.nz' => ['MEGA网盘', '#eab308'],
        'drive.google.com' => ['Google Drive', '#16a34a'],
        'onedrive.live.com' => ['OneDrive', '#0ea5e9'],
        '1drv.ms' => ['OneDrive', '#0ea5e9'],
        'dropbox.com' => ['Dropbox', '#0284c7'],
        'mediafire.com' => ['MediaFire', '#ef4444'],
        'yandex.disk' => ['Yandex.Disk', '#eab308'],
        'disk.yandex' => ['Yandex.Disk', '#eab308'],
        'yandex.ru/disk' => ['Yandex.Disk', '#eab308'],
        'cloud.mail.ru' => ['Mail.ru', '#0ea5e9'],
        'pcloud.com' => ['pCloud', '#0284c7'],
        'box.com' => ['Box', '#2563eb'],
        'app.box.com' => ['Box', '#2563eb'],
        'sync.com' => ['Sync.com', '#2c80c3'],
        'amazon.com/drive' => ['Amazon Drive', '#ff9900'],
        'amazon.com/clouddrive' => ['Amazon Drive', '#ff9900'],
        'terabox.com' => ['TeraBox', '#4338ca'],
        'idrive.com' => ['iDrive', '#0073cf'],
        'degoo.com' => ['Degoo', '#ff4f1c'],
        'nextcloud.com' => ['Nextcloud', '#0082c9'],
        'owncloud.org' => ['ownCloud', '#1d2d44'],
        'resilio.com' => ['Resilio Sync', '#3b82f6'],
        
        // 代码仓库
        'github.com' => ['GitHub', '#24292e'],
        'gitee.com' => ['Gitee', '#c71d23'],
        'gitlab.com' => ['GitLab', '#fc6d26']
    ];
    
    // 通用网盘关键词
    private $genericKeywords = [
        'pan', 'cloud', 'disk', 'yun', 'share', 'down', 'download', 'file', 
        '网盘', '云盘', '分享', '下载', '传输', '存储'
    ];
    
    /**
     * 转换内容中的网盘链接
     */
    public function convert($content) {
        // 防止空内容或已处理内容
        if (empty($content) || strpos($content, 'class="download-btn"') !== false) {
            return $content;
        }
        
        // 测试URL识别 - 仅用于调试
        /* 
        $testUrls = [
            'https://xjrjk.lanzout.com/iI1iO2vncquf',  // 蓝奏云变种
            'https://www.lanzoux.com/abcdef',          // 常规蓝奏云
            'https://pan.baidu.com/s/abcdefg',         // 百度网盘
            'https://123pan.com/s/abcd-efgh',          // 123网盘
            'https://www.123684.com/s/AK6Mjv-x8pAA',   // 123数字网盘
            'https://feijix.com/s/abcdefg',            // 飞机盘
            'https://www.feijixyz.com/s/123456',       // 飞机盘变种
            'https://example.com/download/file.zip',   // 本地ZIP下载
            'https://example.com/files/document.pdf',  // 本地PDF下载
            'https://example.com/download.html'        // 非网盘/非下载URL
        ];
        
        foreach ($testUrls as $url) {
            $result = $this->identifyNetdisk($url);
            error_log("Testing URL: $url - Result: " . ($result ? $result[0] : 'Not recognized'));
        }
        */
        
        try {
            // 创建DOM解析器
            $dom = new DOMDocument();
            
            // 设置错误级别，避免HTML5标签警告
            libxml_use_internal_errors(true);
            
            // 为内容添加根元素，便于处理
            $dom->loadHTML('<?xml encoding="UTF-8"><div>' . $content . '</div>', LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
            libxml_clear_errors();
            
            // 获取文档根元素
            $rootNode = $dom->getElementsByTagName('div')->item(0);
            
            // 处理链接
            $this->processNodes($rootNode);
            
            // 获取处理后的HTML
            $processedContent = '';
            $children = $rootNode->childNodes;
            foreach ($children as $child) {
                $processedContent .= $dom->saveHTML($child);
            }
            
            return $processedContent;
        } catch (Exception $e) {
            error_log('网盘链接处理错误: ' . $e->getMessage());
            return $content; // 出错时返回原始内容
        }
    }
    
    /**
     * 递归处理DOM节点
     */
    private function processNodes($node, $inAnchor = false) {
        // 如果节点是文本节点且不在锚点内，处理纯文本URL
        if ($node->nodeType === XML_TEXT_NODE && !$inAnchor) {
            $text = $node->nodeValue;
            $processed = $this->processTextContent($text, $node->ownerDocument);
            
            if ($text !== $processed) {
                $fragment = $node->ownerDocument->createDocumentFragment();
                $fragment->appendXML($processed);
                $node->parentNode->replaceChild($fragment, $node);
                return;
            }
        }
        
        // 如果是链接节点，检查是否是网盘链接
        if ($node->nodeName === 'a') {
            $href = $node->getAttribute('href');
            $dataUrl = $node->getAttribute('data-url');
            $url = !empty($dataUrl) ? $dataUrl : $href;
            $linkText = $node->textContent;
            
            // 如果是网盘链接，替换为下载按钮
            $netdiskInfo = $this->identifyNetdisk($url, $linkText);
            if ($netdiskInfo) {
                // 提取链接文本和可能的提取码
                $text = $node->textContent;
                $extractCode = $this->extractCode($url, $text);
                
                // 替换为下载按钮
                $button = $this->createDownloadButton($node->ownerDocument, $url, $netdiskInfo[0], $netdiskInfo[1], $extractCode);
                $node->parentNode->replaceChild($button, $node);
                return;
            }
            
            // 递归处理锚点内的子节点
            $childNodes = $node->childNodes;
            foreach ($childNodes as $childNode) {
                $this->processNodes($childNode, true);
            }
            return;
        }
        
        // 递归处理其他节点的子节点
        if ($node->hasChildNodes()) {
            // 需要使用副本进行迭代，因为节点列表在处理过程中可能会改变
            $childNodes = [];
            foreach ($node->childNodes as $childNode) {
                $childNodes[] = $childNode;
            }
            
            foreach ($childNodes as $childNode) {
                $this->processNodes($childNode, $inAnchor);
            }
        }
    }
    
    /**
     * 处理纯文本内容，识别并转换网盘链接
     */
    private function processTextContent($text, $document) {
        // 更强大的URL正则表达式，能捕获各种形式的URL
        $urlPattern = '/\b((?:https?:\/\/|www\.)[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*))/i';
        
        return preg_replace_callback($urlPattern, function($matches) use ($document) {
            $url = $matches[1];
            // 确保URL以http或https开头
            if (strpos($url, 'http') !== 0) {
                $url = 'https://' . $url;
            }
            
            $surroundingText = $matches[0];
            $netdiskInfo = $this->identifyNetdisk($url, '', $surroundingText);
            
            if ($netdiskInfo) {
                // 提取可能的提取码
                $extractCode = $this->extractCode($url, substr($matches[0], 0, 200));
                
                // 创建下载按钮
                $button = $this->createDownloadButton($document, $url, $netdiskInfo[0], $netdiskInfo[1], $extractCode);
                return $document->saveHTML($button);
            }
            
            return $matches[0];
        }, $text);
    }
    
    /**
     * 识别网盘类型
     */
    private function identifyNetdisk($url, $linkText = '', $surroundingText = '') {
        if (empty($url)) {
            return null;
        }
        
        // 检测本地下载链接 - 压缩文件和常见可下载文件格式
        $localDownloadExtensions = [
            // 压缩文件
            'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 
            'tgz', 'tbz2', 'tar.gz', 'tar.bz2', 'tar.xz',
            // 可执行文件
            'exe', 'msi', 'apk', 'dmg', 'iso',
            // 文档文件
            'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
            // 媒体文件
            'mp3', 'mp4', 'wav', 'flac', 'avi', 'mov', 'mkv'
        ];
        
        foreach ($localDownloadExtensions as $ext) {
            if (preg_match('/\.' . $ext . '(\?.*)?$/i', $url)) {
                // 优先使用链接文本作为文件名（如果不是空的且不是URL自身）
                $filename = '';
                
                // 尝试从链接文本中获取文件名
                if (!empty($linkText) && $linkText != $url) {
                    $filename = $linkText;
                }
                
                // 如果链接文本为空，尝试从URL中提取文件名
                if (empty($filename)) {
                    $filename = $this->extractFilename($url);
                }
                
                return ['本地下载-'.$filename, '#222222'];
            }
        }
        
        // 解析URL域名
        $urlInfo = parse_url($url);
        $host = isset($urlInfo['host']) ? $urlInfo['host'] : '';
        $path = isset($urlInfo['path']) ? $urlInfo['path'] : '';
        
        // 优先进行完整URL匹配
        $fullUrl = strtolower($host . $path);
        
        // 精确匹配特定域名（针对常见变种）
        $specificDomains = [
            'lanzout.com' => ['蓝奏云盘', '#06b6d4'],
            '123684.com' => ['123网盘', '#8b5cf6'],
            'feijix.com' => ['飞机盘', '#6366f1'],
            'kdocs.cn' => ['金山文档', '#0284c7'],
            'yuque.com' => ['语雀文档', '#25b864'],
            'alist.nn.ci' => ['Alist网盘', '#12b7f5']
        ];
        
        foreach ($specificDomains as $domain => $info) {
            if (stripos($fullUrl, $domain) !== false) {
                return $info;
            }
        }
        
        // 直接匹配网盘域名 - 使用完整域名+路径匹配，提高精度
        foreach ($this->netdiskMap as $domain => $info) {
            if (stripos($fullUrl, $domain) !== false) {
                return $info;
            }
        }
        
        // 飞机盘变种域名
        if (preg_match('/feiji[a-z0-9]*\.com/i', $fullUrl) || 
            preg_match('/www\.feiji[a-z0-9]*\.com/i', $fullUrl) || 
            preg_match('/[a-z0-9-_]+\.feiji[a-z0-9]*\.com/i', $fullUrl)) {
            return ['飞机盘', '#6366f1'];
        }
        
        // 精确匹配123数字组合域名
        if (preg_match('/123[0-9]+\.com/i', $fullUrl) || 
            preg_match('/www\.123[0-9]*\.com/i', $fullUrl) || 
            preg_match('/[a-z0-9-_]+\.123[0-9]*\.com/i', $fullUrl)) {
            return ['123网盘', '#8b5cf6'];
        }
        
        // 蓝奏云变种域名 - 更全面的正则表达式匹配
        if (preg_match('/(lan(zou|zo|zous|zoux|zoui|zoul|zoum|zouw|zouo|zoup|zouj|zout)|xjrjk)[^\/]*\.com/i', $fullUrl)) {
            return ['蓝奏云盘', '#06b6d4'];
        }
        
        // 星云网盘变种
        if (preg_match('/xing(yun|cloud)[0-9]*\./i', $fullUrl)) {
            return ['蓝奏云盘', '#06b6d4'];
        }
        
        // 123网盘变种 - 通用模式
        if (preg_match('/123(pan|yun|disk|cloud|[0-9]+)[\.a-z0-9-_]*/i', $fullUrl)) {
            return ['123网盘', '#8b5cf6'];
        }
        
        // 更通用的蓝奏云域名匹配（任意子域名+任意变种后缀）
        if (preg_match('/[a-z0-9-_]+\.lanz[a-z0-9]*\.com/i', $fullUrl)) {
            return ['蓝奏云盘', '#06b6d4'];
        }
        
        // 最后尝试匹配任意子域名+lanzXXX这样的形式
        if (preg_match('/\w+\.lanz[a-z0-9]+\./i', $fullUrl)) {
            return ['蓝奏云盘', '#06b6d4'];
        }
        
        // 尝试基于路径格式识别网盘类型
        $pathPatterns = [
            '/\/s\/[a-zA-Z0-9-_]{5,}/i' => '通用网盘分享',
            '/\/share\/[a-zA-Z0-9-_]{5,}/i' => '通用网盘分享',
            '/\/f\/[a-zA-Z0-9-_]{5,}/i' => '文件分享',
            '/\/d\/[a-zA-Z0-9-_]{5,}/i' => '下载分享',
            '/\/file\/[a-zA-Z0-9-_]{5,}/i' => '文件分享'
        ];
        
        foreach ($pathPatterns as $pattern => $type) {
            if (preg_match($pattern, $path)) {
                // 通用网盘路径格式 - 尝试从域名猜测网盘类型
                $possibleNetdisk = $this->guessNetdiskTypeFromDomain($host);
                if ($possibleNetdisk) {
                    return $possibleNetdisk;
                }
                
                // 检查是否包含网盘常见关键词
                $urlLower = strtolower($url);
                foreach ($this->genericKeywords as $keyword) {
                    if (stripos($urlLower, $keyword) !== false) {
                        return ['网盘分享', '#4a5568']; // 更改默认颜色和名称
                    }
                }
                
                return ['文件分享', '#4a5568']; // 如果无法识别具体网盘，使用更通用的标签
            }
        }
        
        // 检查通用网盘关键词
        $urlLower = strtolower($url);
        foreach ($this->genericKeywords as $keyword) {
            if (stripos($urlLower, $keyword) !== false) {
                // 确认URL确实像网盘链接（包含分享或下载字样）
                if (preg_match('/(\/s\/|\/share|\/file|\/d\/|\.zip|\.rar|\/download)/i', $url)) {
                    $possibleNetdisk = $this->guessNetdiskTypeFromDomain($host);
                    return $possibleNetdisk ? $possibleNetdisk : ['网盘分享', '#4a5568'];
                }
            }
        }
        
        return null;
    }
    
    /**
     * 根据域名猜测网盘类型
     */
    private function guessNetdiskTypeFromDomain($host) {
        // 从域名中提取可能的网盘名称
        $domainParts = explode('.', strtolower($host));
        if (count($domainParts) > 1) {
            $mainDomain = $domainParts[count($domainParts) - 2]; // 取主域名部分
            
            // 常见域名对应的网盘
            $domainMap = [
                'baidu' => ['百度网盘', '#3b82f6'],
                'aliyu' => ['阿里云盘', '#f59e0b'],
                'alipan' => ['阿里云盘', '#f59e0b'],
                '189' => ['天翼云盘', '#0ea5e9'],
                'lanzou' => ['蓝奏云盘', '#06b6d4'],
                'feiji' => ['飞机盘', '#6366f1'],
                '123pan' => ['123网盘', '#8b5cf6'],
                'quark' => ['夸克网盘', '#10b981'],
                'weiyun' => ['微云网盘', '#14b8a6'],
                '115' => ['115网盘', '#f43f5e'],
                'ctfile' => ['城通网盘', '#ec4899'],
                'cow' => ['奶牛快传', '#0284c7'],
                'jianguo' => ['坚果云盘', '#2563eb'],
                '360' => ['360云盘', '#22c55e'],
                'xunlei' => ['迅雷云盘', '#0ea5e9'],
                'xiaomi' => ['小米云盘', '#f97316'],
                'mi' => ['小米云盘', '#f97316'],
                '139' => ['移动云盘', '#22c55e'],
                'wo' => ['联通云盘', '#16a34a'],
                'uc' => ['UC网盘', '#ffa100'],
                'sina' => ['新浪微盘', '#f43f5e'],
                'weibo' => ['新浪微盘', '#f43f5e'],
                'tmp' => ['TMP.link', '#8b5cf6'],
                'bitqiu' => ['比特球云盘', '#f59e0b'],
                'bilnn' => ['比邻云盘', '#0ea5e9'],
                'icloud' => ['iCloud云盘', '#3b82f6'],
                'apple' => ['iCloud云盘', '#3b82f6'],
                'firefox' => ['火狐网盘', '#ff7139'],
                'wenshu' => ['文叔叔', '#4c1d95'],
                'wps' => ['WPS云盘', '#0284c7'],
                'kdocs' => ['金山文档', '#0284c7'],
                'huawei' => ['华为云空间', '#d22222'],
                'caiyun' => ['天翼云盘', '#0ea5e9'],
                'yuque' => ['语雀文档', '#25b864'],
                'quqi' => ['曲奇云盘', '#e11d48'],
                'alist' => ['Alist网盘', '#12b7f5'],
                'kod' => ['可道云', '#0ea5e9'],
                'webdav' => ['WebDAV网盘', '#3b82f6'],
                'seafile' => ['Seafile网盘', '#0c64c0'],
                'mega' => ['MEGA网盘', '#eab308'],
                'google' => ['Google Drive', '#16a34a'],
                'onedrive' => ['OneDrive', '#0ea5e9'],
                'dropbox' => ['Dropbox', '#0284c7'],
                'mediafire' => ['MediaFire', '#ef4444'],
                'yandex' => ['Yandex.Disk', '#eab308'],
                'mail' => ['Mail.ru', '#0ea5e9'],
                'pcloud' => ['pCloud', '#0284c7'],
                'box' => ['Box', '#2563eb'],
                'sync' => ['Sync.com', '#2c80c3'],
                'amazon' => ['Amazon Drive', '#ff9900'],
                'terabox' => ['TeraBox', '#4338ca'],
                'idrive' => ['iDrive', '#0073cf'],
                'degoo' => ['Degoo', '#ff4f1c'],
                'nextcloud' => ['Nextcloud', '#0082c9'],
                'owncloud' => ['ownCloud', '#1d2d44'],
                'resilio' => ['Resilio Sync', '#3b82f6'],
                'github' => ['GitHub', '#24292e'],
                'gitee' => ['Gitee', '#c71d23'],
                'gitlab' => ['GitLab', '#fc6d26']
            ];
            
            // 逐一尝试匹配域名
            foreach ($domainMap as $key => $value) {
                if (stripos($mainDomain, $key) !== false || (count($domainParts) > 2 && stripos($domainParts[count($domainParts) - 3], $key) !== false)) {
                    return $value;
                }
            }
            
            // 检查子域名是否有匹配
            if (count($domainParts) > 2) {
                $subdomain = $domainParts[0];
                foreach ($domainMap as $key => $value) {
                    if (stripos($subdomain, $key) !== false) {
                        return $value;
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 从URL中提取文件名
     */
    private function extractFilename($url) {
        // 移除URL参数
        $url = preg_replace('/\?.*$/', '', $url);
        
        // 尝试从路径中提取文件名
        if (preg_match('/\/([^\/]+\.[a-zA-Z0-9]+)$/', $url, $matches)) {
            $filename = $matches[1];
            // 解码URL编码
            $filename = urldecode($filename);
            // 限制文件名长度，避免按钮过长
            if (mb_strlen($filename) > 20) {
                $filename = mb_substr($filename, 0, 17) . '...';
            }
            return $filename;
        }
        
        // 如果无法提取文件名，返回默认值
        return '文件';
    }
    
    /**
     * 从URL或文本中提取提取码
     */
    private function extractCode($url, $text = '') {
        // 从URL参数中提取
        if (preg_match('/[?&](?:提取码|pwd|password|密码|key|访问码|提取|pass|k|code)[:=]([^&\s]{2,8})/i', $url, $matches)) {
            return $matches[1];
        }
        
        // 从文本中提取
        if (!empty($text)) {
        $patterns = [
                '/(?:提取码|密码|访问码|验证码)[:：]\s*([a-zA-Z0-9]{2,8})/ui',
                '/(?:pwd|password|key|code)[:：=]\s*([a-zA-Z0-9]{2,8})/i',
                '/(?:提取码|密码|pwd)[^\w]*?([a-zA-Z0-9]{2,8})(?:\s|$)/ui'
        ];

        foreach ($patterns as $pattern) {
                if (preg_match($pattern, $text, $matches)) {
                    return trim($matches[1]);
                }
            }
        }
        
        return '';
    }
    
    /**
     * 创建下载按钮DOM元素
     */
    private function createDownloadButton($document, $url, $type, $color, $extractCode = '') {
        // 确保有默认颜色，防止白色透明按钮问题
        if (empty($color) || $color == '#ffffff' || $color == 'white' || $color == 'transparent') {
            $color = '#4a5568'; // 默认使用深灰色
        }
        
        // 创建按钮容器
        $button = $document->createElement('a');
        $button->setAttribute('href', $url);
        $button->setAttribute('class', 'download-btn');
        $button->setAttribute('style', 'background-color: ' . $color . '; color: white; display: inline-flex; align-items: center;');
        $button->setAttribute('target', '_blank');
        $button->setAttribute('rel', 'nofollow');
        
        if ($extractCode) {
            $button->setAttribute('data-code', $extractCode);
        }
        
        // 创建图标
        $svg = $document->createElementNS('http://www.w3.org/2000/svg', 'svg');
        $svg->setAttribute('xmlns', 'http://www.w3.org/2000/svg');
        $svg->setAttribute('width', '18');
        $svg->setAttribute('height', '18');
        $svg->setAttribute('viewBox', '0 0 24 24');
        $svg->setAttribute('fill', 'none');
        $svg->setAttribute('stroke', 'currentColor');
        $svg->setAttribute('stroke-width', '2');
        $svg->setAttribute('stroke-linecap', 'round');
        $svg->setAttribute('stroke-linejoin', 'round');
        $svg->setAttribute('style', 'margin-right: 5px;'); // 确保图标和文字之间有间距
        
        $path = $document->createElementNS('http://www.w3.org/2000/svg', 'path');
        $path->setAttribute('stroke-linecap', 'round');
        $path->setAttribute('stroke-linejoin', 'round');
        $path->setAttribute('d', 'M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4');
        $path->setAttribute('style', 'fill:none;');
        
        $svg->appendChild($path);
        $button->appendChild($svg);
        
        // 创建文本部分
        $span = $document->createElement('span');
        $span->setAttribute('style', 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;');
        
        // 如果有提取码，添加到按钮文本
        if ($extractCode) {
            $span->nodeValue = $type . ' 提取码: ' . $extractCode;
        } else {
            $span->nodeValue = $type;
        }
        
        $button->appendChild($span);
        
        return $button;
    }
}

/**
 * 转换下载链接的入口函数
 */
function convertDownloadLinks($content) {
    if (empty($content)) {
        return $content;
    }
    
    // 初始化资源
    DownloadButtonHandler::init();
    
    // 使用新版转换器
    static $converter = null;
    if ($converter === null) {
    $converter = new DownloadButtonConverter();
    }
    
    $result = $converter->convert($content);
    return !empty($result) ? $result : $content;
}