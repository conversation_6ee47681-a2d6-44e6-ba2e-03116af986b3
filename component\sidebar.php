<?php if (!defined("__TYPECHO_ROOT_DIR__")) {
  exit();
} ?>

<div class="sidebar__right__inner flex flex-col px-3 md:px-5">
    <style>
    /* 社群按钮样式 */
    .community-buttons {
        margin: 1.5rem 0;
        padding: 1rem;
        background: rgba(245, 245, 245, 0.1);
        border-radius: 12px;
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .community-buttons a {
        display: flex;
        align-items: center;
        padding: 16px 20px;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: white !important;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* QQ按钮样式 */
    .community-buttons .qq-btn {
        background-image: linear-gradient(135deg, #ffc107, #ffab00);
    }
    
    /* 微信按钮样式 */
    .community-buttons .wechat-btn {
        background-image: linear-gradient(135deg, #07c160, #059b4a);
    }
    
    /* Telegram按钮样式 */
    .community-buttons .telegram-btn {
        background-image: linear-gradient(135deg, #2ba4e2, #1d8bc7);
    }

    .community-buttons a:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.25);
        filter: brightness(1.1);
    }

    .community-buttons iconify-icon {
        font-size: 26px;
        margin-right: 15px;
        filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.15));
    }

    /* 暗色模式下的按钮样式 */
    .dark .community-buttons a {
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    }

    .dark .community-buttons a {
        background: rgba(31, 41, 55, 0.9);
        border-color: rgba(255, 255, 255, 0.1);
    }

    /* 微信群弹窗样式 */
    .wechat-group-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: flex-end;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .wechat-group-modal.show {
        display: flex;
        opacity: 1;
        visibility: visible;
    }

    .wechat-group-modal-content {
        background: white;
        border-radius: 8px;
        padding: 20px;
        width: 320px;
        height: 100%;
        max-height: 480px;
        margin-right: -320px;
        transition: margin-right 0.3s ease;
    }

    .wechat-group-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .wechat-group-modal-close {
        background: none;
        border: none;
        cursor: pointer;
        padding: 4px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: color 0.2s;
    }

    .wechat-group-modal-close:hover {
        color: #333;
    }

    .wechat-group-modal-body {
        text-align: center;
    }

    .wechat-group-tip {
        font-size: 0.9em;
        color: #666;
        margin: 10px 0 0;
    }

    .dark .wechat-group-modal-content {
        background: #1f2937;
        color: #e0e0e0;
    }

    .dark .wechat-group-modal-close {
        color: #e0e0e0;
    }

    .dark .wechat-group-modal-close:hover {
        color: #fff;
    }

    .dark .wechat-group-tip {
        color: #999;
    }

    /* 基础模块样式优化 */
    .sidebar__right__inner > div {
        padding: 1.25rem 0;
        border-bottom: 1px solid rgb(245, 245, 244);
        position: relative;
        width: 100%;
        max-width: 100%;
        overflow: hidden; /* 防止内容溢出 */
    }
    
    /* 响应式布局调整 */
    @media (max-width: 768px) {
        .sidebar__right__inner {
            padding: 0 1rem;
        }
        
        .tag-cloud {
            max-height: none; /* 移动端不限制标签云高度 */
        }
    }
    
    /* 优化标签云响应式表现 */
    .tag-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 0 4px;
        width: 100%;
    }
    
    .tag-item {
        flex: 0 1 auto; /* 允许标签自动缩放 */
        max-width: 100%; /* 防止标签超出容器 */
    }
    
    /* 间距控制 */
    .sidebar__right__inner > div::before,
    .sidebar__right__inner > div::after {
        content: '';
        display: block;
        height: 10px;
    }
    
    .sidebar__right__inner > div::before {
        margin-top: -10px;
    }
    
    .sidebar__right__inner > div::after {
        margin-bottom: -10px;
    }
    
    /* 暗色模式 */
    .dark .sidebar__right__inner > div {
        border-bottom: 1px solid rgb(82, 82, 82);
    }
    
    /* 标签云样式统一 */
    .tag-cloud {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 0 8px;
        max-height: calc(5 * (1.875rem + 8px)); /* 5行的最大高度：每行高度+间距 */
        overflow: hidden;
    }
    
    .tag-item {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        height: 1.875rem;
        font-size: 0.875rem;
        color: rgb(75, 85, 99);
        background-color: rgb(243, 244, 246);
        border-radius: 9999px;
        transition: all 0.3s ease;
    }
    
    .dark .tag-item {
        color: rgb(156, 163, 175);
        background-color: rgb(31, 41, 55);
    }
    
    .tag-text {
        font-size: 0.875rem;
        line-height: 1.25rem;
    }
    
    /* 统一的悬停效果 - 修改这部分 */
    .tag-item:hover,
    .sidebar__right__inner a:hover,
    .sidebar__right__inner li:hover > a {
        background-color: #333333 !important; /* 改为黑色 */
        color: white !important;
        transform: translateY(-1px);
        transition: all 0.3s ease;
    }
    
    /* 添加链接的基础样式 */
    .sidebar__right__inner a {
        transition: all 0.3s ease;
    }
    
    /* 暗色模式下的悬停效果 */
    .dark .tag-item:hover,
    .dark .sidebar__right__inner a:hover,
    .dark .sidebar__right__inner li:hover > a {
        background-color: #1a1a1a !important;
        color: white !important;
    }
    
    /* 特殊链接的悬停效果（如图标链接） */
    .sidebar__right__inner .group:hover iconify-icon {
        color: white !important;
    }
    
    /* 统一的链接基础样式 */
    .sidebar__right__inner a {
        transition: all 0.3s ease;
        border-radius: 9999px; /* 统一圆角 */
        padding: 4px 12px; /* 统一内边距 */
    }
    
    /* 热门文章链接特殊样式 */
    .sidebar__right__inner .popular-article {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 4px 12px;
        border-radius: 9999px;
        width: 100%;
    }
    
    /* 统一的悬停效果 */
    .tag-item:hover,
    .sidebar__right__inner a:hover,
    .sidebar__right__inner li:hover > a,
    .sidebar__right__inner .popular-article:hover {
        background-color: #333333 !important;
        color: white !important;
        transform: translateY(-1px);
        transition: all 0.3s ease;
    }
    
    /* 悬停时图标颜色统一为白色 */
    .sidebar__right__inner a:hover iconify-icon,
    .sidebar__right__inner .popular-article:hover iconify-icon,
    .sidebar__right__inner .popular-article:hover span {
        color: white !important;
    }
    
    /* 文章目录样式，已移动到独立CSS文件中 */
    </style>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "Author", true)): ?>
    <div class="flex flex-col gap-y-5">
        <div class="flex gap-x-3">
            <div class="shrink-0">
                <img src="<?php echo getAvatarByMail($this->author->mail, true); ?>"
                     loading="lazy"
                     alt="<?php $this->author->screenName(); ?>" 
                     class="w-[50px] h-[50px] rounded-full object-cover"
                >
            </div>
            <div class="flex flex-col justify-between">
                <p class="jasmine-primary-color font-medium">
                    <?php $this->author->screenName(); ?>
                </p>
                <p class="line-clamp-2 text-sm text-gray-500 dark:text-gray-400">
                    <?php $this->options->authorRecommend(); ?>
                </p>
            </div>
        </div>
        <?php if ($authorTag = $this->options->authorTag): ?>
            <ul class="flex flex-wrap gap-x-2 gap-y-2">
                <?php foreach (explode(",", $authorTag) as $tag): ?>
                    <li class="bg-stone-200 rounded py-1 px-2 text-sm dark:bg-black dark:text-neutral-400">
                        <?php echo $tag; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php // 文章目录组件，从外部文件引入 ?>
    <?php if ($this->is('post') || $this->is('page')): ?>
    <?php $this->need('component/article-toc.php'); ?>
    <?php endif; ?>

    <script>
// 微信二维码弹窗控制
function showWechatQrCode(qrCodeUrl) {
    const modal = document.createElement('div');
    modal.className = 'wechat-group-modal';
    modal.innerHTML = `
        <div class='wechat-group-modal-content'>
            <div class='wechat-group-modal-header'>
                <h3 class='text-lg font-semibold'>扫码加入微信群</h3>
                <button class='wechat-group-modal-close' onclick='closeWechatQrCodeModal(this)'>
                    <iconify-icon icon="tabler:x" class="text-xl"></iconify-icon>
                </button>
            </div>
            <div class='wechat-group-modal-body'>
                <img src='${qrCodeUrl}' 
                     class='mx-auto w-48 h-48 object-cover rounded-lg'
                     alt='微信群二维码'>
                <p class='wechat-group-tip'>请使用微信扫描二维码</p>
            </div>
        </div>
    `;
    
    modal.addEventListener('click', (e) => {
        if (e.target.className === 'wechat-group-modal') {
            closeWechatQrCodeModal(modal);
        }
    });
    
    document.body.appendChild(modal);
    
    // 显示模态框
    setTimeout(() => {
        modal.classList.add('show');
        setTimeout(() => {
            modal.querySelector('.wechat-group-modal-content').style.marginRight = '0';
        }, 10);
    }, 0);
}

function closeWechatQrCodeModal(element) {
    const modal = element.closest('.wechat-group-modal') || element;
    const modalContent = modal.querySelector('.wechat-group-modal-content');
    
    // 先滑出
    modalContent.style.marginRight = '-320px';
    
    // 等待动画完成后移除元素
    setTimeout(() => {
        modal.classList.remove('show');
        setTimeout(() => {
            if (modal.parentNode) {
                document.body.removeChild(modal);
            }
        }, 300);
    }, 300);
}
</script>

<?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "Community", true)): ?>
<div class="flex flex-col justify-start gap-x-3 gap-y-4 mb-6">
    <div class="flex items-center gap-x-2">
        <h3 class="text-xl font-bold jasmine-primary-color">
            <iconify-icon icon="tabler:users" class="rounded pr-1 text-xl font-medium"></iconify-icon>
            加入社群
        </h3>
    </div>
    <div class="community-buttons">
        <?php if ($this->options->qqGroup): ?>
        <a href="<?php $this->options->qqGroup(); ?>" target="_blank" class="qq-btn">
            <iconify-icon icon="ri:qq-fill"></iconify-icon>
            <span>QQ交流群</span>
        </a>
        <?php endif; ?>
        <?php if ($this->options->wechatGroup): ?>
        <a href="javascript:;" onclick="showWechatQrCode('<?php $this->options->wechatGroup(); ?>')" class="wechat-btn">
            <iconify-icon icon="ri:wechat-fill"></iconify-icon>
            <span>微信沟通群</span>
        </a>
        <?php endif; ?>
        <?php if ($this->options->telegramGroup): ?>
        <a href="<?php $this->options->telegramGroup(); ?>" target="_blank" class="telegram-btn">
            <iconify-icon icon="ri:telegram-fill"></iconify-icon>
            <span>Telegram群组</span>
        </a>
        <?php endif; ?>
    </div>
</div>
<?php endif; ?>
<?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "PopularArticles", true)): ?>

    <div class="flex flex-col justify-start gap-x-3 gap-y-4 mb-6">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:flame" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                热门文章
            </h2>
        </div>
        <?php 
        $db = Typecho_Db::get();
        $prefix = $db->getPrefix();
        
        // 修改查询以获取完整的文章信息
        $posts = $db->fetchAll($db->select()
            ->from('table.contents')
            ->where('type = ?', 'post')
            ->where('status = ?', 'publish')
            ->order('views', Typecho_Db::SORT_DESC)
            ->limit(5)
        );
        
        if (!empty($posts)): ?>
            <div class="flex flex-col gap-y-2 px-2">
                <?php 
                foreach ($posts as $post): 
                    // 创建文章对象以获取正确的永久链接
                    $post = Typecho_Widget::widget('Widget_Abstract_Contents')->push($post);
                ?>
                    <a href="<?php echo $post['permalink']; ?>" 
                       class="popular-article">
                        <span class="flex-1 line-clamp-1 text-sm dark:text-gray-400 text-neutral-500">
                            <?php echo subString($post['title'], 20); ?>
                        </span>
                        <span class="text-gray-400">
                            <iconify-icon icon="tabler:link" class="text-lg"></iconify-icon>
                        </span>
                    </a>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <div class="text-gray-500 dark:text-gray-400">暂无热门文章</div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "PopularCategories", false)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4 mb-6">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:category" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                热门分类
            </h2>
        </div>
        <ul class="flex flex-wrap gap-y-2 px-2">
            <?php $this->widget("Widget_Metas_Category_List", "ignoreZeroCount=1&limit=15")->to($categories); ?>
            <?php if ($categories->have()): ?>
                <?php while ($categories->next()): ?>
                    <li>
                        <a href="<?php $categories->permalink(); ?>"
                           title="<?php $categories->name(); ?>"
                           class="dark:text-gray-400 text-sm rounded-full px-3 py-1">
                           <?php $categories->name(); ?>
                        </a>
                    </li>
                <?php endwhile; ?>
            <?php endif; ?>
        </ul>
    </div>
    <?php endif; ?>
    
    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "LatestComments", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4 mb-6">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:message-circle-2" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                最新评论
            </h2>
        </div>
        <div class="flex flex-col gap-y-2 px-2" id="recent-comments">
            <?php 
            $recentComments = getRecentComments(5);
            if (!empty($recentComments)): 
                foreach ($recentComments as $comment): 
            ?>
                <a href="<?php echo $comment['permalink']; ?>" 
                   class="flex items-center gap-x-1 hover:bg-transparent group">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100 w-[80px] text-right truncate group-hover:text-[#3273dc]">
                        <?php echo mb_substr($comment['author'], 0, 10, 'UTF-8'); ?>
                    </div>
                    <span class="text-gray-400 dark:text-gray-500 group-hover:text-[#3273dc]">：</span>
                    <div class="text-sm text-gray-500 dark:text-gray-400 line-clamp-1 flex-1 group-hover:text-[#3273dc]">
                        <?php echo mb_substr($comment['text'], 0, 30, 'UTF-8'); ?>
                    </div>
                </a>
            <?php 
                endforeach;
            else: 
            ?>
                <div class="text-gray-500 dark:text-gray-400">暂无评论</div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "PopularTags", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4 mb-6">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:tags" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                热门标签
            </h2>
        </div>
        <div class="tag-cloud">
            <?php 
            $this->widget("Widget_Metas_Tag_Cloud", "ignoreZeroCount=1&limit=30")->to($tags);
            $tagCount = 0;
            $maxLength = mb_strlen("技术教程", 'UTF-8'); // 用作标准长度
            
            if ($tags->have()):
                while ($tags->next()):
                    // 只显示不超过标准长度的标签
                    if (mb_strlen($tags->name, 'UTF-8') <= $maxLength):
                        $tagCount++;
            ?>
                <a href="<?php $tags->permalink(); ?>" 
                   class="tag-item"
                   title="<?php echo $tags->name; ?>">
                    <span class="tag-text"># <?php echo $tags->name; ?></span>
                </a>
            <?php 
                    endif;
                endwhile;
            endif;
            ?>
        </div>
    </div>
    <?php endif; ?>

    <?php if (inArrayOptionValueOrDefault("sidebarRightWidget", "About", true)): ?>
    <div class="flex flex-col justify-start gap-x-3 gap-y-4 mb-6">
        <div class="flex items-center gap-x-2">
            <h2 class="text-xl font-bold jasmine-primary-color">
                <iconify-icon icon="tabler:user" class="rounded pr-1 text-xl font-medium"></iconify-icon>
                关于站长
            </h2>
        </div>
        <ul class="flex flex-col gap-y-3 px-2 dark:text-gray-400">
            <?php if ($this->options->wx): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:brand-wechat" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->wx(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->qq): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:brand-qq" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->qq(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->location): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:map-pin" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->location(); ?></span>
                </li>
            <?php endif; ?>

            <?php if ($this->options->email): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:mail" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->email(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->career): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:briefcase" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->career(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->github): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:brand-github" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->github(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->link): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:link" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->link(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->cc): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:badge-cc" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><?php $this->options->cc(); ?></span>
                </li>
            <?php endif; ?>
            <?php if ($this->options->icpCode): ?>
                <li class="flex flex-row items-center gap-x-2">
                    <iconify-icon icon="tabler:id-badge-2" class="text-gray-800 dark:text-gray-300"></iconify-icon>
                    <span class="text-sm "><a href="https://beian.miit.gov.cn/" target="_blank"><?php $this->options->icpCode(); ?></a></span>
                </li>
            <?php endif; ?>
        </ul>
    </div>
    <?php endif; ?>

</div>
