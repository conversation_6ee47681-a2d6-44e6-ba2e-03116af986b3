<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<div class="related-posts bg-white dark:bg-[#161829] rounded-lg p-4 md:p-6 border border-stone-100 dark:border-neutral-600">
    <div class="mb-4 md:mb-6">
        <h5 class="text-lg font-medium text-black dark:text-gray-300 flex items-center justify-center">
            <iconify-icon icon="tabler:heart" class="mr-2 text-xl text-red-500"></iconify-icon>猜你喜欢
        </h5>
    </div>
    
    <?php
    // 获取热门文章并随机推荐
    $db = Typecho_Db::get();
    $monthAgo = time() - 30 * 24 * 60 * 60; // 一个月前的时间戳
    
    try {
        // 获取一个月内的热门文章
        $hotPosts = $db->fetchAll($db
            ->select()->from('table.contents')
            ->where('status = ?', 'publish')
            ->where('type = ?', 'post')
            ->where('created > ?', $monthAgo)
            ->order('views', Typecho_Db::SORT_DESC)
            ->limit(30)
        );
        
        if (!empty($hotPosts)):
            // 准备文章数据
            $posts = [];
            foreach ($hotPosts as $post) {
                $postObj = Typecho_Widget::widget('Widget_Abstract_Contents')->push($post);
                $posts[] = [
                    'permalink' => $postObj['permalink'],
                    'title' => $post['title'],
                ];
            }
            
            // 随机打乱并只取6篇
            shuffle($posts);
            $posts = array_slice($posts, 0, 6);
            
            // 将文章分成左右两列
            $leftPosts = [];
            $rightPosts = [];
            foreach ($posts as $index => $post) {
                if ($index % 2 == 0) {
                    $leftPosts[] = ['index' => $index, 'post' => $post];
                } else {
                    $rightPosts[] = ['index' => $index, 'post' => $post];
                }
            }
    ?>
        <div class="flex">
            <div class="w-1/2 pr-2 md:pr-3">
                <?php foreach ($leftPosts as $item): ?>
                    <a href="<?php echo $item['post']['permalink']; ?>" class="recommend-item">
                        <span class="index"><?php echo $item['index'] + 1; ?></span>
                        <span class="title"><?php echo $item['post']['title']; ?></span>
                    </a>
                <?php endforeach; ?>
            </div>
            <div class="w-1/2 pl-2 md:pl-3">
                <?php foreach ($rightPosts as $item): ?>
                    <a href="<?php echo $item['post']['permalink']; ?>" class="recommend-item">
                        <span class="index"><?php echo $item['index'] + 1; ?></span>
                        <span class="title"><?php echo $item['post']['title']; ?></span>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php 
        else: 
    ?>
        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
            <iconify-icon icon="tabler:alert-circle" class="block mx-auto text-2xl mb-2"></iconify-icon>
            暂无推荐文章
        </div>
    <?php 
        endif;
    } catch (Exception $e) {
        echo '<div class="text-center py-4 text-gray-500 dark:text-gray-400">';
        echo '<iconify-icon icon="tabler:alert-circle" class="block mx-auto text-2xl mb-2"></iconify-icon>';
        echo '暂无推荐文章';
        echo '</div>';
    }
    ?>
</div>

<style>
.recommend-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 8px;
    border-radius: 6px;
    transition: all 0.2s;
    color: #333;
}

.dark .recommend-item {
    color: #e0e0e0;
}

.recommend-item:hover {
    transform: translateY(-2px);
    background-color: rgba(59, 130, 246, 0.08);
}

.recommend-item .index {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
    min-width: 22px;
    margin-right: 8px;
    border-radius: 50%;
    background: #eee;
    color: #666;
    font-size: 13px;
}

.dark .recommend-item .index {
    background: #374151;
    color: #aaa;
}

.recommend-item:hover .index {
    background: #3b82f6;
    color: white;
}

.dark .recommend-item:hover .index {
    background: #60a5fa;
    color: #1e293b;
}

.recommend-item .title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
}

.recommend-item:hover .title {
    color: #3b82f6;
}

.dark .recommend-item:hover .title {
    color: #60a5fa;
}

@media (max-width: 640px) {
    .recommend-item {
        padding: 6px;
    }
    .recommend-item .title {
        font-size: 13px;
    }
    .recommend-item .index {
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .recommend-item {
        padding: 4px;
    }
    .recommend-item .title {
        font-size: 12px;
    }
    .recommend-item .index {
        width: 18px;
        height: 18px;
        font-size: 11px;
        margin-right: 6px;
    }
}
</style> 